# 使用 JDK8 运行时
FROM openjdk:8-jdk

# 基本环境（时区/编码 + 预留可调参数）
ENV TZ=Asia/Shanghai \
    LANG=C.UTF-8 \
    JAVA_OPTS="" \
    APP_OPTS=""

# 统一工作目录
WORKDIR /app

# 常用挂载点：临时目录 + 日志目录（便于落盘）
VOLUME /tmp
VOLUME /app/logs

# 可通过 --build-arg 定制 JAR 路径
ARG JAR_FILE=target/bto-cloud-gateway.jar
COPY ${JAR_FILE} /app/app.jar

# 暴露网关端口（对应 server.port=58080）
EXPOSE 58080

# 允许外部注入 JVM/应用参数（如 --spring.profiles.active=dev）
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar $APP_OPTS"]
