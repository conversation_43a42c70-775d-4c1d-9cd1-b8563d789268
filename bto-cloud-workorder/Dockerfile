# 基础镜像
FROM openjdk:8-jdk

# 设置时区和语言（避免日志时间错误）
ENV TZ=Asia/Shanghai \
    LANG=C.UTF-8 \
    JAVA_OPTS="" \
    APP_OPTS=""

# 创建工作目录（可选）
WORKDIR /app

# 挂载点（临时目录、日志目录）
VOLUME /tmp
VOLUME /app/logs

# 复制Jar（支持构建时传参）
ARG JAR_FILE=target/bto-cloud-workorder.jar
COPY ${JAR_FILE} app.jar

# 暴露端口（对应 application.yml 中的 server.port）
EXPOSE 58085

# 启动命令（允许外部通过 JAVA_OPTS / APP_OPTS 调整参数）
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app.jar $APP_OPTS"]
