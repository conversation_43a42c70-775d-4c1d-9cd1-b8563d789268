# 使用 JDK8 运行时环境
FROM openjdk:8-jdk

# 设置时区和语言，避免容器里时间/编码错乱
ENV TZ=Asia/Shanghai \
    LANG=C.UTF-8 \
    JAVA_OPTS="" \
    APP_OPTS=""

# 应用工作目录
WORKDIR /app

# 挂载目录（临时目录 + 日志目录）
VOLUME /tmp
VOLUME /app/logs

# 构建时可传递 JAR 包路径（默认 target 下）
ARG JAR_FILE=target/bto-cloud-system.jar
COPY ${JAR_FILE} app.jar

# 暴露服务端口（对应 application.yml 中的 server.port=58081）
EXPOSE 58081

# 启动命令，支持 JVM 参数和应用参数
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar $APP_OPTS"]
